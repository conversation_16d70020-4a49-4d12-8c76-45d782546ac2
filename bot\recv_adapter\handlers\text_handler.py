"""
文本消息处理器 - 专门处理私聊文本消息
"""
from loguru import logger
from framework.core.models import StandardMessage


class TextMessageHandler:
    """文本消息处理器"""

    def __init__(self):
        pass

    async def handle_text_message(self, message: StandardMessage) -> bool:
        """
        处理私聊文本消息

        Args:
            message: 标准化后的消息

        Returns:
            bool: 是否成功处理
        """
        try:
            logger.info(f"Processing private text message from {message.from_user_name}: {message.content}")

            # 简单的私聊文本消息处理
            # 这里可以添加具体的处理逻辑，比如转发到其他系统
            return True

        except Exception as e:
            logger.error(f"Error handling text message {message.msg_id}: {e}")
            return False

