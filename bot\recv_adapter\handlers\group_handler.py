"""
群组消息处理器 - 专门处理群组相关的消息和事件
"""
from loguru import logger
from framework.core.models import StandardMessage


class GroupMessageHandler:
    """群组消息处理器"""

    def __init__(self):
        pass
    
    async def handle_group_message(self, message: StandardMessage) -> bool:
        """
        处理群组消息

        Args:
            message: 标准化后的消息

        Returns:
            bool: 是否成功处理
        """
        try:
            group_id = getattr(message, 'group_id', None)
            if not group_id:
                logger.warning(f"Group message without group_id: {message.msg_id}")
                return False

            logger.info(f"Processing group message in {group_id} from {message.from_user_name}: {message.content}")

            # 简单的群组消息处理
            # 这里可以添加具体的处理逻辑，比如转发到其他系统
            return True

        except Exception as e:
            logger.error(f"Error handling group message {message.msg_id}: {e}")
            return False

