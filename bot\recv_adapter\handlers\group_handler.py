"""
群组消息处理器 - 专门处理群组相关的消息和事件
"""
from typing import Dict, Any, List, Optional, Set
from loguru import logger

from framework.core.models import StandardMessage


class GroupMessageHandler:
    """群组消息处理器"""
    
    def __init__(self):
        # 群组配置缓存
        self.group_configs: Dict[str, Dict[str, Any]] = {}
        
        # 活跃群组列表
        self.active_groups: Set[str] = set()
        
        # 管理员列表缓存
        self.group_admins: Dict[str, Set[str]] = {}
        
        # 群组功能开关
        self.group_features = {
            'auto_reply': True,
            'keyword_response': True,
            'welcome_new_member': True,
            'anti_spam': True,
        }
    
    async def handle_group_message(self, message: StandardMessage) -> bool:
        """
        处理群组消息
        
        Args:
            message: 标准化后的消息
            
        Returns:
            bool: 是否成功处理
        """
        try:
            group_id = getattr(message, 'group_id', None)
            if not group_id:
                logger.warning(f"Group message without group_id: {message.msg_id}")
                return False
            
            # 更新活跃群组
            self.active_groups.add(group_id)
            
            # 获取群组配置
            group_config = await self.get_group_config(group_id)
            
            # 检查群组是否启用机器人
            if not group_config.get('bot_enabled', True):
                logger.debug(f"Bot disabled in group {group_id}")
                return True
            
            # 根据消息类型处理
            if message.msg_name == "ModContacts":
                return await self.handle_group_info_change(message)
            elif message.msg_name == "DelContacts":
                return await self.handle_member_leave(message)
            else:
                return await self.handle_group_chat_message(message, group_config)
                
        except Exception as e:
            logger.error(f"Error handling group message {message.msg_id}: {e}")
            return False
    
    async def handle_group_chat_message(self, message: StandardMessage, group_config: Dict[str, Any]) -> bool:
        """处理群聊消息"""
        group_id = message.group_id
        from_user = message.from_user_name
        
        logger.info(f"Group chat message in {group_id} from {from_user}")
        
        # 检查是否是机器人自己的消息
        if message.is_self_message:
            return True
        
        # 检查是否被@
        if hasattr(message, 'at_list') and message.at_list:
            if message.wxid in message.at_list:
                return await self.handle_bot_mention(message, group_config)
        
        # 检查是否是OpenIM消息
        if from_user.endswith("@openim"):
            return await self.handle_openim_group_message(message, group_config)
        
        # 反垃圾检查
        if group_config.get('anti_spam', True):
            if await self.is_spam_message(message):
                return await self.handle_spam_message(message)
        
        # 关键词响应
        if group_config.get('keyword_response', True):
            if await self.check_keyword_response(message, group_config):
                return True
        
        # 普通群消息处理
        return await self.handle_normal_group_message(message, group_config)
    
    async def handle_bot_mention(self, message: StandardMessage, group_config: Dict[str, Any]) -> bool:
        """处理@机器人的消息"""
        logger.info(f"Bot mentioned in {message.group_id} by {message.from_user_name}")
        
        content = message.content
        
        # 移除@信息获取纯文本
        clean_content = self.clean_mention_content(content)
        
        # 检查是否是管理命令
        if await self.is_admin_user(message.group_id, message.from_user_name):
            if await self.handle_admin_command(message, clean_content):
                return True
        
        # 普通@消息处理
        return await self.handle_mention_response(message, clean_content, group_config)
    
    async def handle_openim_group_message(self, message: StandardMessage, group_config: Dict[str, Any]) -> bool:
        """处理群组中的OpenIM消息"""
        logger.info(f"OpenIM group message in {message.group_id}: {message.content}")
        
        # TODO: 实现OpenIM群消息处理逻辑
        return True
    
    async def handle_group_info_change(self, message: StandardMessage) -> bool:
        """处理群信息变更"""
        logger.info(f"Group info changed: {message.content}")
        
        # 解析群信息变更类型
        if "修改群名" in message.content:
            return await self.handle_group_name_change(message)
        elif "成为新群主" in message.content:
            return await self.handle_group_owner_change(message)
        elif "移出群聊" in message.content:
            return await self.handle_member_kicked(message)
        
        return True
    
    async def handle_member_leave(self, message: StandardMessage) -> bool:
        """处理成员离开群组"""
        logger.info(f"Member left group: {message.content}")
        
        # TODO: 实现成员离开处理逻辑
        return True
    
    async def handle_spam_message(self, message: StandardMessage) -> bool:
        """处理垃圾消息"""
        logger.warning(f"Spam message detected in {message.group_id}: {message.msg_id}")
        
        # TODO: 实现垃圾消息处理逻辑（警告、踢出等）
        return True
    
    async def handle_admin_command(self, message: StandardMessage, command: str) -> bool:
        """处理管理员命令"""
        logger.info(f"Admin command in {message.group_id}: {command}")
        
        command_lower = command.lower().strip()
        
        if command_lower.startswith('开启'):
            return await self.handle_enable_feature(message, command_lower)
        elif command_lower.startswith('关闭'):
            return await self.handle_disable_feature(message, command_lower)
        elif command_lower == '状态':
            return await self.handle_group_status(message)
        elif command_lower == '帮助':
            return await self.handle_admin_help(message)
        
        return False
    
    async def handle_mention_response(self, message: StandardMessage, content: str, group_config: Dict[str, Any]) -> bool:
        """处理@消息的响应"""
        logger.info(f"Processing mention response: {content}")
        
        # TODO: 实现@消息响应逻辑
        return True
    
    async def handle_normal_group_message(self, message: StandardMessage, group_config: Dict[str, Any]) -> bool:
        """处理普通群消息"""
        logger.debug(f"Normal group message: {message.content}")
        
        # TODO: 实现普通群消息处理逻辑
        return True
    
    async def get_group_config(self, group_id: str) -> Dict[str, Any]:
        """获取群组配置"""
        if group_id not in self.group_configs:
            # 加载默认配置
            self.group_configs[group_id] = {
                'bot_enabled': True,
                'auto_reply': True,
                'keyword_response': True,
                'welcome_new_member': True,
                'anti_spam': True,
                'admin_only_commands': True,
            }
        
        return self.group_configs[group_id]
    
    async def is_admin_user(self, group_id: str, user_id: str) -> bool:
        """检查用户是否是群管理员"""
        if group_id not in self.group_admins:
            # TODO: 从API获取群管理员列表
            self.group_admins[group_id] = set()
        
        return user_id in self.group_admins[group_id]
    
    async def is_spam_message(self, message: StandardMessage) -> bool:
        """检查是否是垃圾消息"""
        content = message.content
        
        # 简单的垃圾消息检测规则
        spam_keywords = ['广告', '推广', '加微信', '免费', '赚钱']
        
        # 检查重复字符
        if len(set(content)) < len(content) * 0.3:
            return True
        
        # 检查垃圾关键词
        for keyword in spam_keywords:
            if keyword in content:
                return True
        
        return False
    
    async def check_keyword_response(self, message: StandardMessage, group_config: Dict[str, Any]) -> bool:
        """检查关键词响应"""
        content = message.content.lower()
        
        # TODO: 实现关键词响应逻辑
        return False
    
    def clean_mention_content(self, content: str) -> str:
        """清理@信息"""
        import re
        # 移除@信息
        cleaned = re.sub(r'@[^\s]+\s*', '', content)
        return cleaned.strip()
    
    async def handle_group_name_change(self, message: StandardMessage) -> bool:
        """处理群名修改"""
        logger.info(f"Group name changed: {message.content}")
        return True
    
    async def handle_group_owner_change(self, message: StandardMessage) -> bool:
        """处理群主变更"""
        logger.info(f"Group owner changed: {message.content}")
        return True
    
    async def handle_member_kicked(self, message: StandardMessage) -> bool:
        """处理成员被踢"""
        logger.info(f"Member kicked: {message.content}")
        return True
    
    async def handle_enable_feature(self, message: StandardMessage, command: str) -> bool:
        """处理开启功能命令"""
        logger.info(f"Enable feature command: {command}")
        return True
    
    async def handle_disable_feature(self, message: StandardMessage, command: str) -> bool:
        """处理关闭功能命令"""
        logger.info(f"Disable feature command: {command}")
        return True
    
    async def handle_group_status(self, message: StandardMessage) -> bool:
        """处理群状态查询"""
        logger.info(f"Group status query in {message.group_id}")
        return True
    
    async def handle_admin_help(self, message: StandardMessage) -> bool:
        """处理管理员帮助"""
        logger.info(f"Admin help in {message.group_id}")
        return True
