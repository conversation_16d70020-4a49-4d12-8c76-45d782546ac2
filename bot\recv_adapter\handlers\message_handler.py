"""
消息处理器 - 处理不同类型的消息
"""
from typing import Dict, Any, Optional
from loguru import logger

from framework.core.models import StandardMessage, MessageType
from .text_handler import TextMessageHandler
from .group_handler import GroupMessageHandler
from .config import HandlerConfig


class MessageHandler:
    """消息处理器基类"""
    
    def __init__(self):
        # 初始化专门的处理器
        self.text_handler = TextMessageHandler()
        self.group_handler = GroupMessageHandler()
        self.config = HandlerConfig()

        self.handlers = {
            MessageType.文本: self.handle_text_message,
            MessageType.图片: self.handle_image_message,
            MessageType.语音: self.handle_voice_message,
            MessageType.视频: self.handle_video_message,
            MessageType.表情: self.handle_emoji_message,
            MessageType.应用: self.handle_app_message,
            MessageType.文件: self.handle_file_message,
            MessageType.名片: self.handle_card_message,
            MessageType.位置: self.handle_location_message,
        }
    
    async def handle_message(self, message: StandardMessage) -> bool:
        """
        处理消息的主入口

        Args:
            message: 标准化后的消息

        Returns:
            bool: 是否成功处理
        """
        try:
            # 检查消息是否应该被处理
            if not self._should_process_message(message):
                logger.debug(f"Message {message.msg_id} skipped")
                return True

            # 检查是否是特殊消息类型
            if hasattr(message, 'msg_name') and message.msg_name in ["ModContacts", "DelContacts", "Offline"]:
                return await self._handle_special_message(message, message.msg_name)

            # 检查是否是支持的消息类型
            if message.msg_type not in self.handlers:
                logger.debug(f"Unsupported message type: {message.msg_type}")
                return True

            # 调用对应的处理器
            handler = self.handlers[message.msg_type]
            return await handler(message)

        except Exception as e:
            logger.error(f"Error handling message {message.msg_id}: {e}")
            return False

    def _should_process_message(self, message: StandardMessage) -> bool:
        """检查消息是否应该被处理"""
        # 跳过自己的消息
        if message.is_self_message:
            logger.debug(f"Skipping self message: {message.msg_id}")
            return False

        # 跳过打开类型的消息
        if message.msg_type == MessageType.打开:
            logger.debug(f"Skipping open message: {message.msg_id}")
            return False

        return True

    async def _handle_special_message(self, message: StandardMessage, msg_name: str) -> bool:
        """处理特殊消息类型（如联系人变更、掉线等）"""
        logger.info(f"Handling special message: {msg_name}")

        if msg_name == "ModContacts":
            return await self._handle_contact_change(message)
        elif msg_name == "DelContacts":
            return await self._handle_contact_delete(message)
        elif msg_name == "Offline":
            return await self._handle_offline(message)
        else:
            logger.debug(f"Unknown special message type: {msg_name}")
            return True

    async def _handle_contact_change(self, message: StandardMessage) -> bool:
        """处理联系人变更"""
        logger.info(f"Contact changed: {message.content}")
        # TODO: 实现联系人变更处理逻辑
        return True

    async def _handle_contact_delete(self, message: StandardMessage) -> bool:
        """处理联系人删除"""
        logger.info(f"Contact deleted: {message.content}")
        # TODO: 实现联系人删除处理逻辑
        return True

    async def _handle_offline(self, message: StandardMessage) -> bool:
        """处理掉线通知"""
        logger.warning(f"Account offline: {message.content}")
        # TODO: 实现掉线处理逻辑
        return True

    async def handle_text_message(self, message: StandardMessage) -> bool:
        """处理文本消息"""
        logger.info(f"Processing text message: {message.msg_id}")

        # 检查是否是群消息
        if hasattr(message, 'group_id') and message.group_id:
            # 群组文本消息
            if self.config.get_group_config().get('enabled', True):
                return await self.group_handler.handle_group_message(message)
            else:
                logger.debug(f"Group handler disabled for message {message.msg_id}")
                return True
        else:
            # 私聊文本消息
            if self.config.get_text_config().get('enabled', True):
                return await self.text_handler.handle_text_message(message)
            else:
                logger.debug(f"Text handler disabled for message {message.msg_id}")
                return True

    
    async def _route_by_group_or_private(self, message: StandardMessage) -> bool:
        """
        通用的群组/私聊路由方法（用于非文本消息）

        Args:
            message: 消息对象

        Returns:
            bool: 是否成功处理
        """
        # 检查是否是群消息
        if hasattr(message, 'group_id') and message.group_id:
            # 群组消息
            if self.config.get_group_config().get('enabled', True):
                return await self.group_handler.handle_group_message(message)
            else:
                logger.debug(f"Group handler disabled for message {message.msg_id}")
                return True
        else:
            # 私聊消息，使用基本处理
            logger.info(f"Processing private {MessageType(message.msg_type).name} message: {message.msg_id}")
            return True

    async def handle_image_message(self, message: StandardMessage) -> bool:
        """处理图片消息"""
        logger.info(f"Processing image message: {message.msg_id}")
        return await self._route_by_group_or_private(message)

    async def handle_voice_message(self, message: StandardMessage) -> bool:
        """处理语音消息"""
        logger.info(f"Processing voice message: {message.msg_id}")
        return await self._route_by_group_or_private(message)

    async def handle_video_message(self, message: StandardMessage) -> bool:
        """处理视频消息"""
        logger.info(f"Processing video message: {message.msg_id}")
        return await self._route_by_group_or_private(message)

    async def handle_emoji_message(self, message: StandardMessage) -> bool:
        """处理表情消息"""
        logger.info(f"Processing emoji message: {message.msg_id}")
        return await self._route_by_group_or_private(message)

    async def handle_app_message(self, message: StandardMessage) -> bool:
        """处理应用消息（链接、小程序等）"""
        logger.info(f"Processing app message: {message.msg_id}")
        return await self._route_by_group_or_private(message)

    async def handle_file_message(self, message: StandardMessage) -> bool:
        """处理文件消息"""
        logger.info(f"Processing file message: {message.msg_id}")
        return await self._route_by_group_or_private(message)

    async def handle_card_message(self, message: StandardMessage) -> bool:
        """处理名片消息"""
        logger.info(f"Processing card message: {message.msg_id}")
        return await self._route_by_group_or_private(message)

    async def handle_location_message(self, message: StandardMessage) -> bool:
        """处理位置消息"""
        logger.info(f"Processing location message: {message.msg_id}")
        return await self._route_by_group_or_private(message)
